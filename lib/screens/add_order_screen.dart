import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:io';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/smart_svg_icon.dart';
import '../widgets/country_picker.dart';
import '../controllers/add_order_controller.dart';

class AddOrderScreen extends GetView<AddOrderController> {
  const AddOrderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: _buildMobileAppBar(),
              drawer: _buildMobileDrawer(),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              // Left Sidebar
              _buildSidebar(),

              // Main Content
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

   Widget _buildSidebar() {
    return Container(
      width: MySize.size200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            padding: EdgeInsets.all(MySize.size20),
            child: Column(
              children: [
                SmartIcon(
                  assetPath: 'assets/icons/logo_icon.svg',
                  height: MySize.size70,
                  width: MySize.size129,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),

          // Navigation Items
          Expanded(
            child: Obx(
              () => Column(
                children: [
                  _buildNavItem(
                    icon: 'home',
                    label: 'Dashboard',
                    isSelected: controller.selectedNavIndex.value == 0,
                    onTap: () => controller.selectNavItem(0),
                  ),
                  _buildNavItem(
                    icon: 'person',
                    label: 'Customers List',
                    isSelected: controller.selectedNavIndex.value == 1,
                    onTap: () => controller.selectNavItem(1),
                  ),
                  _buildNavItem(
                    icon: 'shopping_cart',
                    label: 'Orders List',
                    isSelected: controller.selectedNavIndex.value == 2,
                    onTap: () => controller.selectNavItem(2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size4,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: ListTile(
        leading: PlatformIcon(
          iconName: icon,
          size: MySize.size20,
          color: isSelected ? AppColors.blackColor : Colors.white,
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? AppColors.blackColor : Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  PreferredSizeWidget _buildMobileAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      leading: Builder(
        builder: (context) => IconButton(
          icon: PlatformIcon(
            iconName: 'menu',
            size: MySize.size24,
            color: AppColors.blackColor,
          ),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          SmartIcon(
            assetPath: 'assets/icons/logo_icon.svg',
            height: MySize.size80,
            width: MySize.size84,
            color: AppColors.blackColor,
          ),
        ],
      ),
      centerTitle: false,
    );
  }

  Widget _buildMobileDrawer() {
    return Drawer(
      backgroundColor: AppColors.primaryColor,
      child: SafeArea(
        child: Column(
          children: [
            // Logo Section
            Container(
              padding: EdgeInsets.all(MySize.size20),
              child: Column(
                children: [
                  SmartIcon(
                    assetPath: 'assets/icons/logo_icon.svg',
                    height: MySize.size100,
                    width: MySize.size100,
                    color: AppColors.blackColor,
                  ),
                ],
              ),
            ),

            // Divider
            Divider(
              color: AppColors.blackColor.withValues(alpha: 0.2),
              thickness: 1,
              indent: MySize.size16,
              endIndent: MySize.size16,
            ),

            // Navigation Items
            Expanded(
              child: Obx(
                () => Column(
                  children: [
                    _buildMobileNavItem(
                      icon: 'home',
                      label: 'Dashboard',
                      isSelected: controller.selectedNavIndex.value == 0,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(0);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'person',
                      label: 'Customers List',
                      isSelected: controller.selectedNavIndex.value == 1,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(1);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'shopping_cart',
                      label: 'Orders List',
                      isSelected: controller.selectedNavIndex.value == 2,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(2);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: MySize.size12,
          vertical: MySize.size4,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size12,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
        child: Row(
          children: [
            PlatformIcon(
              iconName: icon,
              size: MySize.size20,
              color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
            ),
            SizedBox(width: MySize.size12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth <= 768;

        return Container(
          padding: EdgeInsets.all(isMobile ? MySize.size12 : MySize.size24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with Search Bar
              _buildHeader(isMobile: isMobile),

              SizedBox(height: MySize.size24),

              // Form Content
              Expanded(
                child: SingleChildScrollView(
                  child: _buildFormContent(isMobile: isMobile),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader({bool isMobile = false}) {
    return Row(
      children: [
        // Title Section
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add New Order',
                style: TextStyle(
                  fontSize: isMobile ? MySize.size20 : MySize.size24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: MySize.size4),
              Text(
                'Track every document you\'ve placed sorted by date and status.',
                style: TextStyle(
                  fontSize: isMobile ? MySize.size12 : MySize.size14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),

        if (!isMobile) SizedBox(width: MySize.size24),

        // Search Bar (only on desktop)
        if (!isMobile)
          Expanded(
            flex: 1,
            child: Container(
              constraints: BoxConstraints(maxWidth: 300),
              child: CustomTextField(
                controller: controller.searchController,
                hintText: 'Search Orders',
                onChanged: controller.onSearchChanged,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(MySize.size12),
                  child: PlatformIcon(
                    iconName: 'search',
                    size: MySize.size20,
                    color: AppColors.primaryColor,
                  ),
                ),
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
                borderRadius: MySize.size20,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size10,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFormContent({bool isMobile = false}) {
    if (isMobile) {
      return Column(
        children: [
          _buildCustomerDetailsCard(),
          SizedBox(height: MySize.size16),
          _buildBusinessInformationCard(),
          SizedBox(height: MySize.size16),
          _buildOrderDetailsCard(),
          SizedBox(height: MySize.size24),
          _buildAddOrderButton(),
        ],
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column - Customer Details & Business Information
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildCustomerDetailsCard(),
              SizedBox(height: MySize.size24),
              _buildBusinessInformationCard(),
            ],
          ),
        ),

        SizedBox(width: MySize.size24),

        // Right Column - Order Details
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildOrderDetailsCard(),
              SizedBox(height: MySize.size24),
              _buildAddOrderButton(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerDetailsCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'person',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Customer Details',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size20),

          // Customer Name
          _buildFormField(
            label: 'Customer Name',
            controller: controller.customerNameController,
            hintText: 'Enter customer name',
          ),

          SizedBox(height: MySize.size16),

          // Mobile Number
          _buildMobileNumberField(),

          SizedBox(height: MySize.size16),

          // GST Number
          _buildFormField(
            label: 'GST Number',
            controller: controller.gstNumberController,
            hintText: 'Enter GST number',
          ),

          SizedBox(height: MySize.size16),

          // Email ID
          _buildFormField(
            label: 'Email ID',
            controller: controller.emailIdController,
            hintText: 'Enter email address',
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessInformationCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'building',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Business Information',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size20),

          // Business Name
          _buildFormField(
            label: 'Business Name',
            controller: controller.businessNameController,
            hintText: 'Enter Business Name',
          ),

          SizedBox(height: MySize.size16),

          // Business Address
          _buildFormField(
            label: 'Business Address',
            controller: controller.businessAddressController,
            hintText: 'Enter Business Address',
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetailsCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'order',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Order Details',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size20),

          // Product Image Upload
          _buildImageUploadSection(),

          SizedBox(height: MySize.size20),

          // Order ID
          _buildFormField(
            label: 'Order ID',
            controller: controller.orderIdController,
            hintText: 'Auto-generated',
          ),

          SizedBox(height: MySize.size16),

          // Product Name
          _buildFormField(
            label: 'Product Name',
            controller: controller.productNameController,
            hintText: 'Enter product name',
          ),

          SizedBox(height: MySize.size16),

          // Price and Quantity Row
          Row(
            children: [
              Expanded(
                child: _buildFormField(
                  label: 'Price',
                  controller: controller.priceController,
                  hintText: 'Enter price',
                  prefixText: '₹ ',
                ),
              ),
              SizedBox(width: MySize.size16),
              Expanded(
                child: _buildFormField(
                  label: 'Quantity',
                  controller: controller.quantityController,
                  hintText: 'Enter quantity',
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size16),

          // Payment and Order Status Row
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  label: 'Payment',
                  value: controller.selectedPayment,
                  items: controller.paymentOptions,
                  onChanged: controller.selectPayment,
                ),
              ),
              SizedBox(width: MySize.size16),
              Expanded(
                child: _buildDropdownField(
                  label: 'Order Status',
                  value: controller.selectedOrderStatus,
                  items: controller.orderStatusOptions,
                  onChanged: controller.selectOrderStatus,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    Widget? prefixWidget,
    String? prefixText,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        CustomTextField(
          controller: controller,
          hintText: hintText,
          maxLines: maxLines,
          prefixIcon: prefixWidget ?? (prefixText != null
            ? Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size12),
                child: Center(
                  widthFactor: 1.0,
                  child: Text(
                    prefixText,
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              )
            : null),
          fillColor: Colors.white,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size16,
            vertical: MySize.size12,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required String label,
    required RxString value,
    required List<String> items,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        Obx(
          () => Container(
            padding: EdgeInsets.symmetric(horizontal: MySize.size16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppColors.borderColor),
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: value.value,
                isExpanded: true,
                icon: PlatformIcon(
                  iconName: 'chevron_down',
                  size: MySize.size20,
                  color: AppColors.textSecondary,
                ),
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textPrimary,
                ),
                items: items.map((String item) {
                  return DropdownMenuItem<String>(
                    value: item,
                    child: Text(item),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    onChanged(newValue);
                  }
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Upload Area
        GestureDetector(
          onTap: controller.onUploadImageTap,
          child: Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: BorderRadius.circular(MySize.size8),
              border: Border.all(
                color: AppColors.borderColor,
                style: BorderStyle.solid,
              ),
            ),
            child: Obx(
              () => controller.isUploadingImage.value
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                          ),
                          SizedBox(height: MySize.size12),
                          Text(
                            'Uploading image...',
                            style: TextStyle(
                              fontSize: MySize.size14,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    )
                  : controller.productImagePath.value.isEmpty
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Default product image placeholder
                            Container(
                              width: 80,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(MySize.size4),
                              ),
                              child: Stack(
                                children: [
                                  // Simulate stacked papers
                                  Positioned(
                                    left: 5,
                                    top: 5,
                                    child: Container(
                                      width: 70,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(MySize.size2),
                                        border: Border.all(color: Colors.grey[400]!),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    left: 10,
                                    top: 10,
                                    child: Container(
                                      width: 70,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(MySize.size2),
                                        border: Border.all(color: Colors.grey[400]!),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: MySize.size12),
                            PlatformIcon(
                              iconName: 'camera',
                              size: MySize.size24,
                              color: AppColors.primaryColor,
                            ),
                            SizedBox(height: MySize.size8),
                            Text(
                              'Upload A Product Image',
                              style: TextStyle(
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            SizedBox(height: MySize.size4),
                            Text(
                              'Tap to select from gallery or camera',
                              style: TextStyle(
                                fontSize: MySize.size12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        )
                      : Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(MySize.size8),
                              child: _buildImageWidget(),
                            ),
                            // Remove button
                            Positioned(
                              top: 8,
                              right: 8,
                              child: GestureDetector(
                                onTap: () {
                                  controller.productImagePath.value = '';
                                  controller.productImageUrl.value = '';
                                  controller.productImageBytes.value = null;
                                },
                                child: Container(
                                  padding: EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
            ),
          ),
        ),

        SizedBox(height: MySize.size8),

        // Image format info
        Text(
          'The image format is .jpg, .jpeg, .png and a minimum size of 300 x 300px. (For optimal image use a minimum size of 700 x 700 px).',
          style: TextStyle(
            fontSize: MySize.size12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildAddOrderButton() {
    return SizedBox(
      width: double.infinity,
      child: Obx(
        () => ElevatedButton(
          onPressed: (controller.isLoading.value || controller.isUploadingImage.value)
              ? null
              : controller.onAddOrderTap,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryColor,
            foregroundColor: AppColors.blackColor,
            padding: EdgeInsets.symmetric(vertical: MySize.size16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            elevation: 0,
          ),
          child: controller.isLoading.value
              ? SizedBox(
                  height: MySize.size20,
                  width: MySize.size20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.blackColor),
                  ),
                )
              : Text(
                  controller.isUploadingImage.value
                      ? 'Uploading Image...'
                      : 'Add Order',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildMobileNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mobile Number',
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        Row(
          children: [
            // Country Picker
            Obx(
              () => CountryPicker(
                selectedCountry: controller.selectedCountry.value,
                onCountrySelected: controller.selectCountry,
                width: MySize.size62,
              ),
            ),
            SizedBox(width: MySize.size8),
            // Mobile Number Field
            Expanded(
              child: CustomTextField(
                controller: controller.mobileNumberController,
                hintText: 'Enter mobile number',
                keyboardType: TextInputType.phone,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
                borderRadius: MySize.size8,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size12,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build image widget that works for both web and mobile
  Widget _buildImageWidget() {
    return Obx(() {
      // If we have a Firebase URL, always use that (uploaded image)
      if (controller.productImageUrl.value.isNotEmpty) {
        return Image.network(
          controller.productImageUrl.value,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, color: Colors.red),
                  Text('Failed to load uploaded image'),
                ],
              ),
            );
          },
        );
      }

      // For local preview before upload
      if (kIsWeb && controller.productImageBytes.value != null) {
        // On web, use Image.memory with the bytes
        return Image.memory(
          controller.productImageBytes.value!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, color: Colors.red),
                  Text('Failed to load image preview'),
                ],
              ),
            );
          },
        );
      } else if (!kIsWeb && controller.productImagePath.value.isNotEmpty) {
        // On mobile, use File
        return Image.file(
          File(controller.productImagePath.value),
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, color: Colors.red),
                  Text('Failed to load image preview'),
                ],
              ),
            );
          },
        );
      }

      // Fallback - no image selected
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.image_not_supported, color: Colors.grey),
            Text('No image selected'),
          ],
        ),
      );
    });
  }
}